import { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import Header from './Header';
import MainContent from './MainContent';
import ConversationsPage from './ConversationPage';
import Pay from './pay/Pay';
import PayMobile from './pay/PayMobile';
import PageTitle from './PageTitle';
import { DifyApi, IGetAppParametersResponse } from '../api/src/dify-api';
import { XAiApi, IGetAiAppInfoResponse, PackageByKey, FeeType } from '../api/src/xai-api';
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks';
import { isMobile } from '../api/src/utils';
import Cookies from 'js-cookie';
import { message } from 'antd';

interface ApiProps {
  difyApi: DifyApi
  xAiApi: XAiApi
  currentAppUuid: string
  setCurrentAppUuid: React.Dispatch<React.SetStateAction<string>>
  user: string
}

const Home: React.FC<ApiProps> = ({ difyApi, xAiApi,
  currentAppUuid, setCurrentAppUuid, user }) => {
  const { t } = useSimpleTranslation()
  const { navigateToApp, currentLanguage } = useI18nRouter()

  // 应用列表状态
  const [appList, setAppList] = useState<IGetAiAppInfoResponse[]>([])
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [conversationModalOpen, setConversationModalOpen] = useState(false);
  const [appParam, setAppParam] = useState<IGetAppParametersResponse>()
  const [appListSub, setAppListSub] = useState<Map<string, PackageByKey | null>>(new Map())
  const [showSubscriptionModal, setShowSubscriptionModal] = useState(false)

  // 支付相关状态
  const [payUrl, setPayUrl] = useState('')
  const [paymentLoading, setPaymentLoading] = useState(false)
  const [autoSubscribing, setAutoSubscribing] = useState(false) // 自动订阅状态
  const timeRef = useRef<number | null>(null)

  const { appName } = useParams();
  const navigate = useNavigate();

  // 简化的新对话检测逻辑
  // 由于我们现在使用临时URL（如 new-chat-timestamp），Home组件不再需要处理新对话
  // 新对话会直接路由到ChatDetail组件

  // 获取应用列表
  useEffect(() => {
    const lang = currentLanguage || 'zh';
    Cookies.set("ai_apps_lang", lang);
    const fetchAppList = async () => {
      try {
        const novaxs = await xAiApi.getAppByConfigKey({ configKey: 'novax_apps' });
        const elavaxs = await xAiApi.getAppByConfigKey({ configKey: 'elavax_apps' });
        setAppList([...novaxs, ...elavaxs]);
        setCurrentAppUuid(novaxs[0]?.appUuid || elavaxs[0]?.appUuid || '');
      } catch (error) {
        console.error('获取应用列表失败:', error);
      }
    };

    fetchAppList();
  }, [currentLanguage])

  // 更新API配置
  useEffect(() => {
    const yudaoToken = Cookies.get('yudaoToken')
    if (user && yudaoToken) {
      console.log('Updated user====', user)
      xAiApi.updateOptions({
        user: user,
        apiBase: xAiApi.options.apiBase,
        yudaoToken: yudaoToken
      });
      difyApi.updateOptions({
        user: user,
        apiBase: difyApi.options.apiBase,
        yudaoToken: yudaoToken,
        appId: currentApp?.dAppUuid
      });
    }
  }, [user, currentAppUuid, appList, xAiApi, difyApi]) // 依赖 user, currentAppUuid, appList

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 按优先级关闭弹窗：会话历史 > 搜索 > 侧边栏
        if (conversationModalOpen) {
          setConversationModalOpen(false)
        } else if (searchModalOpen) {
          setSearchModalOpen(false)
        } else if (sidebarOpen) {
          setSidebarOpen(false)
        }
      }
    }

    // 只在有弹窗打开时添加监听器
    if (conversationModalOpen || searchModalOpen || sidebarOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [conversationModalOpen, searchModalOpen, sidebarOpen])

  const login = async() => {
    await xAiApi.logout()
    window.sessionStorage.setItem('redirectUrl', window.location.href)
    if (!currentLanguage || currentLanguage === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  (window as any).addLoginDom?.()
		} else {
		  (window as any).top.location.href =  location.origin + '/' + currentLanguage + "/login"
		}
  };

  const preCheckLogin = () => {
    const yudaoToken = Cookies.get('yudaoToken')
    if (!yudaoToken) {
      console.log('触发登录')
      login()
      return false
    }
    return true
  }

  // 前置检查函数
  const preCheck = () => {
    if (!preCheckLogin()) {
      return false
    }
    // 直接使用已经计算好的 currentApp，而不是重新查找
    if (!currentApp?.appUser || currentApp?.appUser?.status !== '1') {
      console.log('用户未支付，检查是否有免费套餐')

      // 检查是否有免费套餐，如果有则直接自动订阅
      const subStatusDetail = appListSub.get(currentApp.appNameEn)
      const firstPackage = subStatusDetail?.feeTypes?.[0]

      if (firstPackage && (firstPackage.type === '免费')) {
        // 防止重复触发自动订阅
        if (autoSubscribing) {
          console.log('正在自动订阅中，请等待...')
          return false
        }

        console.log('检测到免费套餐，直接触发自动订阅，不显示弹框')
        // 直接设置自动订阅状态，不显示弹框
        setAutoSubscribing(true)

        // 直接调用订阅函数，不弹出Pay组件
        subscribe(firstPackage).then(() => {
          console.log('免费套餐自动订阅完成')
          setAutoSubscribing(false)
        }).catch((error) => {
          console.error('免费套餐自动订阅失败:', error)
          setAutoSubscribing(false)
          // 如果自动订阅失败，弹出支付弹框
          setShowSubscriptionModal(true)
        })

        return false
      } else {
        console.log('没有免费套餐，触发支付弹框')
        setShowSubscriptionModal(true)
        return false
      }
    }
    console.log('已登录，已支付')
    return true
  };

  // 清除支付定时器的函数
  const clearPaymentTimer = () => {
    if (timeRef.current) {
      clearInterval(timeRef.current)
      timeRef.current = null
      console.log('支付状态轮询定时器已清除')
    }
  }

  // 查询支付状态
  const getStatus = async (piId: string) => {
    clearPaymentTimer() // 使用统一的清除函数

    timeRef.current = setInterval(async () => {
      const res = await xAiApi.getSubOrder({ piId })
      if (res.payStatus === 'PAID') {
        location.reload()
        clearPaymentTimer() // 使用统一的清除函数
      }
    }, 2000)
  }

  // 支付方法
  const subscribe = async (feeType: FeeType) => {
    clearPaymentTimer() // 使用统一的清除函数
    if (!preCheckLogin()) {
      console.log('=====', '用户未登录')
      return
    }
    if (!currentApp || !feeType) {
      console.log('=====', '当前应用信息不存在')
      return
    }
    if (paymentLoading) {
      console.log('===支付中')
      return
    }
    const feeSub = appListSub.get(feeType.packageKey)
    if (feeSub?.subStatus === 1 || feeSub?.subStatus === 3) {
        console.log('===已订阅', feeSub)
        return
    }
    const sub = appListSub.get(currentApp.appNameEn)
    if (feeType.type == "免费") {
      if (sub?.subStatus === 1) {
        console.log('===已订阅', currentApp)
        return
      }
    } else {
      if ((sub?.subStatus === 1 && sub?.packageType !== '免费') || sub?.subStatus === 3) {
        console.log('===已订阅', currentApp)
        return
      }
    }

    try {
      setPaymentLoading(true)
      const subscriptionParams = {
        appUuid: currentApp.appUuid || "",
        priceId: feeType.priceId,
        monthNum: feeType.monthNum,
        packageKey: feeType.packageKey,
        packageType: feeType.type
      };
      const payInfo = await xAiApi.createSubscription(subscriptionParams);
      if (feeType.type == "免费") {
        console.log('===自动订阅', payInfo);

        // 免费订阅成功后，更新订阅状态
        await fetchAllAppSubscriptions();

        // 重新计算currentApp - 获取最新的应用信息
        try {
          const updatedAppInfo = await xAiApi.getAppByUuid({ appUuid: currentApp.appUuid });
          setAppList(prevAppList =>
            prevAppList.map(app =>
              app.appUuid === currentApp.appUuid ? updatedAppInfo : app
            )
          );

        } catch (error) {
          console.error('重新获取当前应用信息失败:', error);
        }

        // 短暂延时避免重复订阅
        await new Promise(resolve => setTimeout(resolve, 1000));
      } else {
        // 根据设备类型处理不同的支付逻辑
        if (isMobile()) {
          // 移动端使用支付宝支付
          const resUrl = await xAiApi.createAliSub(JSON.parse(payInfo));
          console.log('------resUrl', resUrl);
          message.success(t('subscription.subscribeSuccess'));
          setTimeout(() => {
            window.location.href = resUrl;
          }, 1000);
        } else {
          // 桌面端使用二维码支付
          const encodedPayInfo = encodeURIComponent(payInfo)
          const newPayUrl = `${window.location.origin}/payLink/${encodedPayInfo}`
          setPayUrl(newPayUrl)
          const piId = JSON.parse(payInfo).piId
          await getStatus(piId);
        }
      }
    } catch (error) {
      console.error(t('subscription.paymentFailed'), error)
    } finally {
      setPaymentLoading(false)
    }
  }

  // 确定当前应用UUID的逻辑
  const effectiveAppUuid = useMemo(() => {
    if (appName) {
      const foundApp = appList.find(app => app.appNameEn === appName);
      if (foundApp) {
        return foundApp.appUuid;
      }
    }
    return appList.length > 0 ? appList[0].appUuid : '';
  }, [appName, appList]);

  useEffect(() => {
    if (effectiveAppUuid && effectiveAppUuid !== currentAppUuid) {
      setCurrentAppUuid(effectiveAppUuid);
    }
  }, [effectiveAppUuid, currentAppUuid, setCurrentAppUuid]);

  const currentApp = useMemo(() => {
    return appList.find(app => app.appUuid === effectiveAppUuid)
  }, [appList, effectiveAppUuid])

  // 获取应用信息和订阅状态
  useEffect(() => {
    if (!currentApp) return;

    const fetchAppInfo = async () => {
      const yudaoToken = Cookies.get('yudaoToken') || '';

      difyApi.updateOptions({
        user: difyApi.options.user,
        apiBase: difyApi.options.apiBase,
        yudaoToken: yudaoToken,
        appId: currentApp.dAppUuid
      });

      const appInfo = await difyApi.getAppParameters();
      setAppParam(appInfo);
      console.log('appInfo====', difyApi.options);
    };

    fetchAppInfo();
  }, [currentApp]);

  // 获取所有app的订阅状态
  const fetchAllAppSubscriptions = async () => {
      if (appList.length === 0) return;

      try {
        console.log('开始获取所有应用的订阅状态');
        const subscriptionPromises = appList.map(async (app) => {
          try {
            const subStatus = await xAiApi.getPackageByKey({ configKey: app.appNameEn });
            return {
              appNameEn: app.appNameEn,
              subStatus
            };
          } catch (err) {
            console.error(`获取应用 ${app.appNameEn} 订阅状态失败:`, err);
            return {
              appNameEn: app.appNameEn,
              subStatus: null
            };
          }
        });

        const allSubscriptions = await Promise.all(subscriptionPromises);

        // 创建Map，以appNameEn为键
        const subscriptionMap = new Map<string, PackageByKey | null>();
        allSubscriptions.forEach(({ appNameEn, subStatus }) => {
          subscriptionMap.set(appNameEn, subStatus);
        });

        setAppListSub(subscriptionMap);
      } catch (error) {
        console.error('批量获取订阅状态失败:', error);
      }
    };

  // 免费版到Pro版的映射关系
  const getProVersionMapping = (appNameEn: string): string | null => {
    const mappings: Record<string, string> = {
      'novax-base': 'novax-pro',
      'elavax-base': 'elavax-pro',
      // 可以根据需要添加更多映射
    }
    return mappings[appNameEn] || null
  }

  // 获取所有app的订阅状态
  useEffect(() => {
    fetchAllAppSubscriptions();
  }, [xAiApi, appList]);

  const onAppSelect = useCallback((appUuid: string) => {
    // 通过国际化路由导航切换应用，而不是直接设置状态
    const targetApp = appList.find(app => app.appUuid === appUuid || app.appNameEn === appUuid);
    if (targetApp) {
      console.log('切换应用到:', targetApp.appNameEn);
      navigateToApp(targetApp.appNameEn);
    }
  }, [appList, navigateToApp]);

  // 监听showSubscriptionModal状态变化，处理免费订阅逻辑
  useEffect(() => {
    if (showSubscriptionModal && currentApp) {
      // 检查第一个套餐是否为免费
      const subStatusDetail = appListSub.get(currentApp.appNameEn)
      if (subStatusDetail?.subStatus === 1 || subStatusDetail?.subStatus === 3) {
        // 已订阅其他版本或退订
        // 检查是否已订阅免费版
        if (subStatusDetail?.packageType === '免费') {
          console.log('用户已订阅免费版，检查是否需要切换到Pro版')

          // 获取对应的Pro版应用名称
          const proAppName = getProVersionMapping(currentApp.appNameEn)
          if (proAppName) {
            onAppSelect(proAppName);
            // setShowSubscriptionModal(false)
          }
        }
        return;
      }


    }
  }, [showSubscriptionModal, currentApp, appListSub, onAppSelect]);

  if (!currentApp) return null; // 组件加载失败时返回空或加载提示

  const handleMainClick = () => {
    if (sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      {/* 页面标题 */}
      <PageTitle
        pageType="home"
        appKey={currentApp?.appNameEn}
      />

      <div className="flex h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
      {/* 移动端遮罩层 */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* 左侧边栏 - 响应式 */}
      <div className={`
        fixed md:relative inset-y-0 left-0 z-40 md:z-auto
        transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0 transition-transform duration-300 ease-in-out
      `}>
        <Sidebar
          onSearchClick={() => setSearchModalOpen(true)}
          onClose={() => setSidebarOpen(false)}
          onConversationClick={() => setConversationModalOpen(true)}
          difyApi={difyApi}
          currentApp={currentApp}
        />
      </div>

      {/* 主要内容区域 */}
      <main className="flex-1 flex flex-col w-full md:w-auto" onClick={handleMainClick}>
        {/* 顶部标题栏 - 固定定位 */}
        <Header
          onMenuClick={() => setSidebarOpen(!sidebarOpen)}
          currentApp={currentApp}
          onAppSelect={onAppSelect}
          appList={appList}
          xAiApi={xAiApi}
          subStatusDetail={appListSub.get(currentApp.appNameEn)!}
          showSubscriptionModal={showSubscriptionModal}
          setShowSubscriptionModal={setShowSubscriptionModal}
          preCheck={preCheck}
        />

        {/* 主要内容 - 添加顶部间距避免被固定头部遮挡，允许滚动 */}
        <div className="pt-16 md:pt-20 flex-1 overflow-y-auto">
          <MainContent
            onAppSelect={onAppSelect}
            difyApi={difyApi}
            xAiApi={xAiApi}
            currentApp={currentApp}
            appList={appList}
            appParam={appParam}
            appListSub={appListSub}
            preCheck={preCheck}
          />
        </div>
      </main>



      {/* 搜索模态框 */}
      {searchModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">搜索</h3>
              <button
                onClick={() => setSearchModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <input
              type="text"
              placeholder="搜索..."
              className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              autoFocus
            />
          </div>
        </div>
      )}

      {/* 会话历史模态框 */}
      {conversationModalOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={() => setConversationModalOpen(false)}
        >
          <div
            className="bg-white rounded-2xl w-full max-w-4xl h-[80vh] flex flex-col shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <ConversationsPage
              difyApi={difyApi}
              currentApp={currentApp}
              onClose={() => setConversationModalOpen(false)}
            />
          </div>
        </div>
      )}



      {/* 订阅弹框 - 根据设备类型显示不同组件 */}
      {currentApp && appListSub.get(currentApp.appNameEn) && !autoSubscribing && (
        isMobile() ? (
          <PayMobile
            isOpen={showSubscriptionModal}
            xAiApi={xAiApi}
            currentApp={currentApp}
            subStatusDetail={appListSub.get(currentApp.appNameEn)!}
            onClose={() => setShowSubscriptionModal(false)}
            subscribe={subscribe}
            paymentLoading={paymentLoading}
          />
        ) : (
          <Pay
            isOpen={showSubscriptionModal}
            xAiApi={xAiApi}
            currentApp={currentApp}
            subStatusDetail={appListSub.get(currentApp.appNameEn)!}
            onClose={() => {
                clearPaymentTimer()
                setShowSubscriptionModal(false)
            }}
            subscribe={subscribe}
            paymentLoading={paymentLoading}
            payUrl={payUrl}
            clearPaymentTimer={clearPaymentTimer}
          />
        )
      )}
      </div>
    </>
  );
};

export default Home;