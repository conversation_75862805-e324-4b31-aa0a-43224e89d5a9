import React, { useRef, useState, useCallback, useEffect } from 'react'
import { ChatUploadFileItem } from './ChatFileUpload'
import { useSimpleTranslation } from '../../i18n/simple-hooks'
import '../file-upload/FileUpload.css'

interface ChatInputProps {
  inputValue: string
  isGenerating: boolean
  onInputChange: (value: string) => void
  onSubmit: () => void
  onKeyDown: (e: React.KeyboardEvent) => void
  onStop: () => void
  onFocus?: () => void
  // 新增：文件上传相关props
  files?: ChatUploadFileItem[]
  onFilesChange?: (files: ChatUploadFileItem[]) => void
  onFileUpload?: (files: File[]) => Promise<void>
  allowedFileTypes?: string[]
  isFileUploadEnabled?: boolean
  isUploading?: boolean
  maxFileSize?: number
  maxFiles?: number
  onValidationError?: (error: string) => void
}

/**
 * 聊天输入组件 - 处理用户输入和消息发送
 * 包含文本输入框、发送按钮、快捷键提示和文件上传功能
 */
const ChatInput: React.FC<ChatInputProps> = ({
  inputValue,
  isGenerating,
  onInputChange,
  onSubmit,
  onKeyDown,
  onStop,
  onFocus,
  files = [],
  onFilesChange,
  onFileUpload,
  allowedFileTypes = [],
  isFileUploadEnabled = false,
  isUploading = false,
  maxFileSize = 50,
  maxFiles,
  onValidationError
}) => {
  const { t } = useSimpleTranslation()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)

  // 确保文件输入元素在组件挂载后立即可用
  useEffect(() => {
    if (isFileUploadEnabled && fileInputRef.current) {
      // 确保文件输入元素的属性正确设置
      const input = fileInputRef.current
      if (!input.multiple) input.multiple = true
    }
  }, [isFileUploadEnabled])

  // 拖拽处理函数
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating) return

    setDragCounter(prev => prev + 1)
    if (e.dataTransfer.items && e.dataTransfer.items.length > 0) {
      setIsDragOver(true)
    }
  }, [isFileUploadEnabled, isGenerating])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating) return

    setDragCounter(prev => {
      const newCounter = prev - 1
      if (newCounter === 0) {
        setIsDragOver(false)
      }
      return newCounter
    })
  }, [isFileUploadEnabled, isGenerating])

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!isFileUploadEnabled || isGenerating) return

    setIsDragOver(false)
    setDragCounter(0)

    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0 && onFileUpload) {
      await onFileUpload(droppedFiles)
    }
  }, [isFileUploadEnabled, isGenerating, onFileUpload])

  return (
    <div className="fixed bottom-0 left-0 right-0 py-1 pb-2 px-6 backdrop-blur-sm z-40" style={{ backgroundColor: 'var(--bg-main)' }}>
      <div className="max-w-4xl mx-auto">
        {/* 主输入框区域 */}
        <div
          className={`relative bg-white rounded-2xl shadow-lg border transition-all duration-300 ${
            isDragOver
              ? 'border-blue-500 bg-blue-50 shadow-2xl ring-2 ring-blue-200'
              : 'border-gray-200 hover:shadow-xl'
          }`}
          onDragEnter={handleDragEnter}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          {/* 文件列表显示区域 - 在输入框上方 */}
          {isFileUploadEnabled && files.length > 0 && (
            <div className="p-4 pb-2 border-b border-gray-100">
              <div className="space-y-2">
                {files.map((file) => (
                  <div
                    key={file.uid}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded-lg border text-sm"
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <div className="flex-shrink-0">
                        {file.type === 'image' ? '🖼️' :
                         file.type === 'document' ? '📄' :
                         file.type === 'audio' ? '🎵' :
                         file.type === 'video' ? '🎬' : '📎'}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">
                          {(file.size / (1024 * 1024)).toFixed(2)} MB
                          {file.status === 'uploading' && ` • ${t('fileUpload.uploading')} ${file.percent}%`}
                          {file.status === 'error' && file.error && ` • ${file.error}`}
                          {file.status === 'done' && ` • ${t('fileUpload.uploadComplete')}`}
                        </p>
                      </div>
                    </div>

                    {/* 状态和删除按钮 */}
                    <div className="flex items-center space-x-2">
                      {file.status === 'uploading' && (
                        <div className="w-16 bg-gray-200 rounded-full h-1.5">
                          <div
                            className="bg-blue-600 h-1.5 rounded-full transition-all duration-300"
                            style={{ width: `${file.percent}%` }}
                          ></div>
                        </div>
                      )}

                      {file.status === 'done' && (
                        <div className="text-green-500">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        </div>
                      )}

                      {file.status === 'error' && (
                        <div className="text-red-500">
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </div>
                      )}

                      <button
                        onClick={() => {
                          if (window.confirm(`确定要删除文件 "${file.name}" 吗？`)) {
                            const updatedFiles = files.filter(f => f.uid !== file.uid)
                            onFilesChange?.(updatedFiles)
                          }
                        }}
                        className="p-1 text-gray-400 hover:text-red-500 transition-colors"
                        disabled={file.status === 'uploading'}
                        title={file.status === 'uploading' ? t('fileUpload.uploadingCannotDelete') : t('fileUpload.removeFile')}
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 文件输入元素 */}
          {isFileUploadEnabled && (
            <>
              {/* 主要的隐藏文件输入 */}
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.txt,.md,.xlsx,.xls,.pptx,.ppt,.csv,.html,.xml,.epub,.json,.jpg,.jpeg,.png,.gif,.webp,.svg,.mp3,.m4a,.wav,.webm,.amr,.mp4,.mov,.mpeg,.mpga"
                onChange={async (e) => {
                  if (isGenerating) {
                    return
                  }
                  const selectedFiles = Array.from(e.target.files || [])
                  if (selectedFiles.length > 0 && onFileUpload) {
                    try {
                      await onFileUpload(selectedFiles)
                    } catch (error) {
                      console.error('文件上传失败:', error)
                    }
                  }
                  // 清空input值，允许重复选择同一文件
                  e.target.value = ''
                }}
                style={{
                  position: 'absolute',
                  left: '-9999px',
                  opacity: 0,
                  pointerEvents: 'none',
                  width: '1px',
                  height: '1px'
                }}
                disabled={isGenerating}
              />


            </>
          )}

          {/* 拖拽提示覆盖层 */}
          {isDragOver && (
            <div className="absolute inset-0 bg-blue-50 bg-opacity-90 rounded-2xl flex items-center justify-center z-10 border-2 border-dashed border-blue-400">
              <div className="text-center p-6">
                <div className="text-4xl mb-3 file-icon-bounce">📎</div>
                <div className="text-blue-600 font-semibold text-lg mb-2">{t('fileUpload.dropToUpload')}</div>
                <div className="text-sm text-blue-500 space-y-1">
                  {maxFiles && (
                    <div className="font-medium">
                      {t('fileUpload.maxFiles').replace('{count}', maxFiles.toString())}
                    </div>
                  )}
                  <div>
                    {allowedFileTypes.length > 0 ? (() => {
                      // 对话详情页格式筛选：仅显示文档类格式
                      const documentFormats = ['pdf', 'doc', 'docx', 'txt', 'md', 'xlsx', 'xls', 'pptx', 'ppt', 'csv', 'html', 'xml', 'epub', 'json'];
                      const filteredFormats = allowedFileTypes.filter(type =>
                        documentFormats.includes(type.toLowerCase())
                      );
                      return filteredFormats.length > 0
                        ? `${t('fileUpload.supportedFormats')}: ${filteredFormats.join(', ')}`
                        : t('fileUpload.multiFileSupport');
                    })() : t('fileUpload.multiFileSupport')}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 文本输入区域 */}
          <textarea
            value={inputValue}
            onChange={(e) => onInputChange(e.target.value)}
            onKeyDown={onKeyDown}
            onFocus={onFocus}
            placeholder={isDragOver ? "" : t('chat.inputPlaceholderDetail')}
            className={`w-full min-h-[140px] p-6 bg-transparent border-0 rounded-2xl resize-none focus:outline-none text-gray-800 text-base leading-relaxed placeholder-gray-400 ${
              isFileUploadEnabled ? 'pl-6 pr-20' : 'pr-20'
            }`}
            disabled={isGenerating}
          />

          {/* 左侧文件上传按钮 */}
          {isFileUploadEnabled && (
            <div className="absolute bottom-4 left-6">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  if (fileInputRef.current) {
                    // 使用setTimeout确保DOM更新完成
                    setTimeout(() => {
                      fileInputRef.current?.click()
                    }, 0)
                  }
                }}
                disabled={isGenerating}
                className="flex !ml-0 !pl-0 items-end justify-center  h-10 rounded-full text-gray-500 hover:text-gray-700    focus:ring-gray-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                title={t('fileUpload.tooltip')}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21.44 11.05l-9.19 9.19a6 6 0 0 1-8.49-8.49l9.19-9.19a4 4 0 0 1 5.66 5.66L9.64 16.2a2 2 0 0 1-2.83-2.83l8.49-8.49"/>
                </svg>
              </button>
            </div>
          )}

          {/* 右侧按钮区域 */}
          <div className="absolute bottom-4 right-4 flex items-center gap-3">
            <div className="text-xs text-gray-400 hidden sm:block">
              {t('chat.enterToSend')}
            </div>
            <button
              type="submit"
              onClick={isGenerating?onStop:onSubmit}
              disabled={!isGenerating && !inputValue.trim()}
              className="relative flex items-center justify-center w-10 h-10 rounded-full text-white focus:outline-none focus:ring-2 focus:ring-opacity-50 disabled:opacity-50 disabled:cursor-not-allowed group shadow-lg hover:shadow-xl transition-all duration-200 ease-out transform hover:scale-105 bg-gradient-to-br from-purple-500 to-blue-500 hover:from-purple-400 hover:to-blue-400 focus:ring-purple-500 hover:shadow-purple-500/30"
            >
              {isGenerating ? (
                <div className="flex items-center justify-center">
                  {/* 简洁的停止图标 */}
                  <div className="w-3 h-3 bg-white rounded-sm animate-spin" style={{animationDuration: '2s'}}></div>
                </div>
              ) : (
                <div className="flex items-center justify-center">
                  {/* 发送图标 */}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="group-hover:animate-[takeoff_1.5s_ease-out_infinite] transition-transform duration-200"
                  >
                    <path d="M22 2L11 13"/>
                    <path d="M22 2L15 22L11 13L2 9L22 2Z"/>
                  </svg>
                </div>
              )}

              {/* 简单的点击反馈效果 */}
              <div className="absolute inset-0 rounded-full bg-white opacity-0 scale-0 group-active:opacity-20 group-active:scale-110 transition-all duration-150"></div>
            </button>
          </div>
        </div>
        {/* 免责声明 */}
        <div className="text-center mt-2">
          <p className="text-xs text-gray-400">
            {t('chat.aiDisclaimer')}
          </p>
        </div>
      </div>
    </div>
  )
}

export default ChatInput