import { useState, useMemo, useEffect, useRef } from 'react'
import { XAiApi, IGetAiAppInfoResponse, PackageByKey } from '../api/src/xai-api';
import Cookies from 'js-cookie';

import { useSimpleTranslation,useI18nRouter } from '../i18n/simple-hooks';
import LanguageSwitcher, { LanguageSwitcherIcon } from './LanguageSwitcher';
import { LogoutIcon } from './icons/Icons';
import { message } from 'antd';

interface HeaderProps {
  onMenuClick?: () => void
  currentApp: IGetAiAppInfoResponse
  onAppSelect: (appUuid: string) => void
  appList: IGetAiAppInfoResponse[]
  xAiApi: XAiApi
  subStatusDetail: PackageByKey | null
  showSubscriptionModal: boolean
  setShowSubscriptionModal: (show: boolean) => void
}

// 用户菜单项将在组件内部使用翻译函数动态生成

const Header = ({
  onMenuClick,
  currentApp,
  onAppSelect,
  appList,
  xAiApi,
  subStatusDetail,
  showSubscriptionModal,
  setShowSubscriptionModal
}: HeaderProps) => {
  const { t } = useSimpleTranslation()
  const defAvatar = 'https://img.medsci.cn/web/img/user_icon.png'
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showVersionMenu, setShowVersionMenu] = useState(false) // 新增状态: 控制版本菜单显示
  const [userInfoString, setUserInfoString] = useState(Cookies.get('userInfo'))
  const [userInfo, setUserInfo] = useState(() => {
    return userInfoString ? JSON.parse(userInfoString) : null
  })
  const [avatar, setAvatar] = useState(userInfo?.avatar || defAvatar)
  const [username, setUsername] = useState(userInfo?.userName || '')
  const { currentLanguage, changeLanguage } = useI18nRouter()

  // 添加ref引用用于点击外部关闭
  const userMenuRef = useRef<HTMLDivElement>(null)
  const versionMenuRef = useRef<HTMLDivElement>(null)
  

  // 动态生成用户菜单项
  const userMenuItems = useMemo(() => [
    // { icon: '⚙️', label: t('navigation.settings'), action: 'settings' },
    // { icon: '💬', label: t('navigation.feedback'), action: 'feedback' },
    // { icon: '📊', label: t('navigation.export'), action: 'export' },
    {
      icon: <LogoutIcon width={16} height={16} className="text-red-500" />,
      label: t('auth.logoutButton'),
      action: 'logout',
      danger: true
    }
  ], [t])

  // 监听用户信息变化
  useEffect(() => {
    const checkUserInfo = () => {
      const currentUserInfo = Cookies.get('userInfo')
      if (currentUserInfo !== userInfoString) {
        setUserInfoString(currentUserInfo)
        if (currentUserInfo) {
          const parsedInfo = JSON.parse(currentUserInfo)
          setUserInfo(parsedInfo)
          setAvatar(parsedInfo.avatar || defAvatar)
          setUsername(parsedInfo.userName || '')
        } else {
          setUserInfo({})
          setAvatar(defAvatar)
          setUsername('')
        }
      }
    }

    // 初始检查
    checkUserInfo()

    // 定期检查用户信息变化（例如其他标签页登录/退出）
    const interval = setInterval(checkUserInfo, 1000)

    return () => clearInterval(interval)
  }, [userInfoString])

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // 检查用户菜单
      if (showUserMenu && userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false)
      }

      // 检查版本菜单
      if (showVersionMenu && versionMenuRef.current && !versionMenuRef.current.contains(event.target as Node)) {
        setShowVersionMenu(false)
      }
    }

    if (showUserMenu || showVersionMenu) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showUserMenu, showVersionMenu])

  const login = async() => {
    await xAiApi.logout()
    window.sessionStorage.setItem('redirectUrl', window.location.href)
		if (!currentLanguage ||  currentLanguage === 'zh') {
		  // 为了解决 TypeScript 类型错误，需要先声明 window.addLoginDom 方法
		  (window as any).addLoginDom?.()
		} else {
		  (window as any).top.location.href =  location.origin + '/' + currentLanguage + "/login"
		}
	}

		const language = () => {
			return Cookies.get('ai_apps_lang') ? Cookies.get('ai_apps_lang') : navigator.language
		}

	const logout = async () => {
		// 清除本地状态
		setUserInfoString(undefined)
		setUserInfo({})
		setAvatar(defAvatar)
		setUsername('')

		await xAiApi.logout()

		// 跳转到登出页面
		if (window.location.origin.includes("medsci.cn")) {
		  (window as any).top.location.href = "https://www.medsci.cn/sso_logout?redirectUrl=" + (location.pathname.split("/").length==4 ?location.origin + location.pathname.substring(0,location.pathname.lastIndexOf('/')) : (window as any).top.location.href);
		} else {
		  (window as any).top.location.href = `https://portal-test.medon.com.cn/sso_logout?redirectUrl=` + (location.pathname.split("/").length==4 ?location.origin + location.pathname.substring(0,location.pathname.lastIndexOf('/')) : (window as any).top.location.href);
		}
	};

  const errorImg = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    console.log('=====', '用户头像加载失败，使用默认头像')
    const target = e.target as HTMLImageElement
    target.src = defAvatar
    setAvatar(defAvatar)
  }

  const handleAllHide = () => {
    setShowUserMenu(false)
    setShowVersionMenu(false)
    if (setShowSubscriptionModal) {
      setShowSubscriptionModal(false)
    }
  }

  const handleVersionMenuToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    // 关闭其他菜单，但保持当前菜单的切换逻辑
    setShowUserMenu(false)
    if (setShowSubscriptionModal) {
      setShowSubscriptionModal(false)
    }
    setShowVersionMenu(prev => !prev)
  }

  const handleUserMenuToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    // 关闭其他菜单，但保持当前菜单的切换逻辑
    setShowVersionMenu(false)
    if (setShowSubscriptionModal) {
      setShowSubscriptionModal(false)
    }
    setShowUserMenu(prev => !prev)
  }

  const handleUserMenuClick = (action: string) => {
    if (action === 'logout') {
      logout()
    }
    
    setShowUserMenu(false)
  }

  // 版本切换处理
  const handleVersionChange = (appUuid: string, appNameEn: string) => {
    if (!appNameEn.includes('base')) {
      message.info(t('chat.comingSoon'))
      console.log('暂时不能对外开放')
      return
    }
    if (onAppSelect) {
      onAppSelect(appUuid)
    }
    console.log('切换版本到:', appUuid)
    setShowVersionMenu(false)
  }

  // 处理订阅按钮点击
  const handleSubscriptionClick = () => {
    if (!userInfo) {
      login()
      return
    }

    // 订阅时显示订阅弹窗
    if (setShowSubscriptionModal && showSubscriptionModal !== undefined) {
      setShowSubscriptionModal(!showSubscriptionModal)
    }
  }

  // 处理订阅弹窗关闭
  const handleSubscriptionClose = () => {
    if (setShowSubscriptionModal) {
      setShowSubscriptionModal(false)
    }
    // 确保按钮失去焦点，防止保持黑色边框
    setTimeout(() => {
      const activeElement = document.activeElement as HTMLElement
      if (activeElement && activeElement.blur) {
        activeElement.blur()
      }
    }, 100)
  }

  // 处理订阅套餐选择
  const handleSubscribe = async (planId: string) => {
    console.log('选择订阅套餐:', planId)
    // 这里可以添加实际的订阅逻辑
    // 例如调用支付接口等
    try {
      // 示例：调用订阅API
      // await xAiApi.subscribe({ planId })
      alert(`已选择订阅套餐: ${planId}`)
    } catch (error) {
      console.error('订阅失败:', error)
      alert('订阅失败，请重试')
    }
  }

  return (
    <>
      <header className="top-0 left-0 right-0 z-50 h-16 border-gray-100 backdrop-blur-md bg-opacity-95 flex items-center justify-between px-4 md:px-8" style={{ backgroundColor: 'var(--bg-main)' }}>
        {/* 左侧Logo区域 */}
        <div className="flex items-center gap-2 md:gap-3">
          {/* 移动端菜单按钮 */}
          {onMenuClick && (
            <button
              onClick={onMenuClick}
              className="h-[40px] w-[30px] flex items-center justify-center text-gray-600 hover:text-gray-900 rounded-lg transition-colors md:hidden"
            >
              <span className="text-sm">☰</span>
            </button>
          )}
          
          {/* 应用切换下拉菜单 - 优化设计 */}
          <div ref={versionMenuRef} className="relative">
            <button
              onClick={handleVersionMenuToggle}
              className="
                group flex items-center space-x-2
                px-3 h-[40px]
                bg-white dark:bg-gray-800
                border border-gray-200 dark:border-gray-600
                rounded-xl shadow-sm
                hover:bg-gray-50 dark:hover:bg-gray-700
                hover:border-gray-300 dark:hover:border-gray-500
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                transition-all duration-200
              "
              aria-expanded={showVersionMenu}
              aria-haspopup="true"
            >
              {/* 应用图标 */}
              <span className="flex-shrink-0 group-hover:scale-110 transition-transform duration-200">
                <img src={currentApp?.appIcon} alt={currentApp?.appName} className="w-5 h-5" />
              </span>

              {/* 应用名称 */}
              <span className="hidden sm:inline font-medium text-sm text-gray-700 dark:text-gray-200 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors duration-200">
                {currentApp?.appName}
              </span>

              {/* 下拉箭头 - 与语言切换器保持一致 */}
              <svg
                className={`w-3.5 h-3.5 text-gray-400 group-hover:text-gray-600 transition-all duration-200 ${
                  showVersionMenu ? 'rotate-180' : ''
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            {/* 应用选择下拉菜单 - 优化设计 */}
            {showVersionMenu && (
              <div
                className="
                  absolute top-full mt-2
                  left-0
                  bg-white dark:bg-gray-800
                  border border-gray-200 dark:border-gray-600
                  rounded-xl shadow-xl
                  z-50 min-w-[220px]
                  overflow-hidden
                  backdrop-blur-sm
                  fade-in
                  ring-1 ring-black ring-opacity-5
                "
                role="menu"
                aria-orientation="vertical"
              >
                {appList.map((app, index) => {
                  const isSelected = currentApp.appUuid === app.appUuid

                  return (
                    <button
                      key={app.appUuid}
                      onClick={() => handleVersionChange(app.appUuid, app.appNameEn)}
                      className={`
                        w-full flex items-center space-x-3 px-4 py-3
                        text-left transition-all duration-200
                        group relative
                        ${!app.appNameEn.includes('base')
                          ? 'bg-gray-50 text-gray-400 cursor-not-allowed hover:bg-gray-50'
                          : isSelected
                            ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                            : 'text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700'
                        }
                        ${index === 0 ? 'rounded-t-xl' : ''}
                        ${index === appList.length - 1 ? 'rounded-b-xl' : ''}
                      `}
                      role="menuitem"
                      aria-selected={isSelected}
                    >
                      {/* 应用图标 */}
                      <span className={`flex-shrink-0 transition-transform duration-200 ${
                        !app.appNameEn.includes('base')
                          ? 'opacity-50'
                          : 'group-hover:scale-110'
                      }`}>
                        <img src={app.appIcon} alt={app.appName} className="w-5 h-5" />
                      </span>

                      {/* 应用名称 */}
                      <span className="font-medium flex-1 text-sm">
                        {app.appName}
                      </span>

                      {/* 选中标记 - 与语言切换器保持一致 */}
                      {isSelected && (
                        <svg
                          className="w-4 h-4 text-blue-500"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </button>
                  )
                })}
              </div>
            )}
          </div>
        </div>
        
        {/* 中间空白区域 */}
        <div className="flex-1"></div>

        {/* 右侧操作区域 - 统一间距布局 */}
        <div className="flex items-center flex-shrink-0 space-x-4 md:space-x-6">

          {/* 订阅按钮 - 只有在有订阅相关props时才显示 */}
          {subStatusDetail && setShowSubscriptionModal && (
            <button
              onClick={handleSubscriptionClick}
              className="gradient-button points modern-button px-3 h-10 flex items-center rounded shadow-md hover:shadow-lg text-sm font-medium"
            >
              <span className="hidden sm:inline">
                {subStatusDetail?.subStatus==1&&subStatusDetail?.packageType=='免费'
                  ? t('subscription.upgrade')
                  : t('subscription.subscribe')
                }
              </span>
              <span><img className="h-6 max-h-6 w-auto object-contain" src="https://img.medsci.cn/202507/81b770ae34d745d29a9b53f0775107db-pWv2rVrLBAg8.png" alt="Subscribe" /></span>
            </button>
          )}

          {/* 语言切换器 */}
          <LanguageSwitcher
            className="h-[40px]"
            showText={false}
            showFlag={true}
            size="medium"
            dropdownPosition="right"
          />

          {/* 用户头像/菜单 */}
          <div ref={userMenuRef} className="relative flex justify-center">
            {!userInfo ? (
              // 未登录状态 - 显示登录按钮
              <button
                onClick={login}
                className="px-3 py-2 bg-gradient-to-r to-indigo-600 text-sm font-medium rounded-lg transition-all duration-200 hover:scale-105"
              >
                {t('auth.loginButton')}
              </button>
            ) : (
              // 已登录状态 - 显示用户头像
              <>
                <button
                  onClick={handleUserMenuToggle}
                  className="w-8 h-8 md:w-10 md:h-10 bg-gradient-to-br rounded-full flex items-center justify-center text-white text-sm md:text-base font-bold hover:shadow-lg transition-all duration-200 hover:scale-105"
                  title={username}
                >
                    <img src={avatar} alt={username} onError={errorImg} className="w-full h-full object-cover rounded-full" />
                </button>

                {/* 用户菜单弹出框 */}
                {showUserMenu && (
                  <div className="user-menu mt-2 fade-in right-0">
                    {/* 用户信息显示 */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-br rounded-full flex items-center justify-center">
                            <img src={avatar} alt={username} onError={errorImg} className="w-full h-full object-cover rounded-full" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">{username || t('header.user')}</div>
                          <div className="text-xs text-gray-500">{t('auth.loggedIn')}</div>
                        </div>
                      </div>
                    </div>

                    {/* 菜单项 */}
                    {userMenuItems.map((item) => (
                      <div
                        key={item.action}
                        onClick={() => handleUserMenuClick(item.action)}
                        className={`user-menu-item ${item.danger ? 'danger' : ''}`}
                      >
                        <span>{item.icon}</span>
                        <span>{item.label}</span>
                      </div>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>


      </header>

    </>
  )
}

export default Header
